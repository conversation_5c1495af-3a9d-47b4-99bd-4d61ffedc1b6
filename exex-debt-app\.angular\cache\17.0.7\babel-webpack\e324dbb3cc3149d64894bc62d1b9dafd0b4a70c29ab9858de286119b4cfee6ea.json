{"ast": null, "code": "export const CUSTOMER_COLS = [{\n  field: 'customerId',\n  title: 'Customer ID',\n  width: '80px'\n}, {\n  field: 'customerCode',\n  title: 'Customer Code',\n  width: '100px'\n}, {\n  field: 'customerName',\n  title: 'Customer Name',\n  width: '150px'\n}, {\n  field: 'address',\n  title: 'Address',\n  width: '150px'\n}, {\n  field: 'phone',\n  title: 'Phone Number',\n  width: '120px'\n}, {\n  field: 'email',\n  title: 'Email',\n  width: '150px'\n}, {\n  field: 'saler',\n  title: 'Saler',\n  width: '80px'\n}, {\n  field: 'creditLimit',\n  title: 'Credit Limit',\n  width: '120px'\n}, {\n  field: 'creditBalance',\n  title: 'Credit Balance',\n  width: '120px'\n}, {\n  field: 'creditDate',\n  title: 'Credit Date',\n  width: '120px'\n}, {\n  field: 'createdBy',\n  title: 'Created By',\n  width: '100px'\n}, {\n  field: 'createdDate',\n  title: 'Created Date',\n  width: '180px'\n}, {\n  field: 'updatedBy',\n  title: 'Updated By',\n  width: '100px'\n}, {\n  field: 'updatedDate',\n  title: 'Updated Date',\n  width: '180px'\n}];", "map": {"version": 3, "names": ["CUSTOMER_COLS", "field", "title", "width"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\dashboard\\customer\\customer-cols.ts"], "sourcesContent": ["export const CUSTOMER_COLS = [\r\n    { field: 'customerId', title: 'Customer ID', width: '80px' },\r\n    { field: 'customerCode', title: 'Customer Code', width: '100px' },\r\n    { field: 'customerName', title: 'Customer Name', width: '150px' },\r\n    { field: 'address', title: 'Address', width: '150px' },\r\n    { field: 'phone', title: 'Phone Number', width: '120px' },\r\n    { field: 'email', title: 'Email', width: '150px' },\r\n    { field: 'saler', title: 'Saler', width: '80px' },\r\n    { field: 'creditLimit', title: 'Credit Limit', width: '120px' },\r\n    { field: 'creditBalance', title: 'Credit Balance', width: '120px' },\r\n    { field: 'creditDate', title: 'Credit Date', width: '120px' },\r\n    { field: 'createdBy', title: 'Created By', width: '100px' },\r\n    { field: 'createdDate', title: 'Created Date', width: '180px' },\r\n    { field: 'updatedBy', title: 'Updated By', width: '100px' },\r\n    { field: 'updatedDate', title: 'Updated Date', width: '180px' },\r\n];\r\n"], "mappings": "AAAA,OAAO,MAAMA,aAAa,GAAG,CACzB;EAAEC,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAM,CAAE,EAC5D;EAAEF,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAO,CAAE,EACjE;EAAEF,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAO,CAAE,EACjE;EAAEF,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAO,CAAE,EACtD;EAAEF,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,EACzD;EAAEF,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAO,CAAE,EAClD;EAAEF,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAM,CAAE,EACjD;EAAEF,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC/D;EAAEF,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE,gBAAgB;EAAEC,KAAK,EAAE;AAAO,CAAE,EACnE;EAAEF,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC7D;EAAEF,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC3D;EAAEF,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC/D;EAAEF,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE,YAAY;EAAEC,KAAK,EAAE;AAAO,CAAE,EAC3D;EAAEF,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAO,CAAE,CAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}