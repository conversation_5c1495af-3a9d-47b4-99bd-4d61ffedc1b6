.login-container {
    display: flex;
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* Left Panel - Brand & Info */
.left-panel {
    flex: 1;
    background: linear-gradient(135deg, #2dd4bf 0%, #14b8a6 50%, #0d9488 100%);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 3rem;
    color: white;
    overflow: hidden;
}

.brand-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 500px;
}

.brand-title {
    font-size: 4rem;
    font-weight: 800;
    margin: 0 0 1rem 0;
    letter-spacing: -0.02em;
    line-height: 1;
}

.brand-subtitle {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 2rem 0;
    line-height: 1.3;
    opacity: 0.95;
}

.brand-description {
    font-size: 1rem;
    line-height: 1.6;
    opacity: 0.9;

    p {
        margin: 0 0 0.5rem 0;
    }
}

/* Decorative circles */
.decorative-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    right: -50px;
}

.circle-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
}

.circle-3 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    right: 5%;
}

/* Bottom modules */
.modules-section {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.module-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem 1.25rem;
    min-width: 120px;
    text-align: center;
    transition: all 0.3s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }
}

.module-title {
    font-weight: 700;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    letter-spacing: 0.5px;
}

.module-subtitle {
    font-size: 0.75rem;
    opacity: 0.9;
    line-height: 1.2;
}

/* Right Panel - Login Form */
.right-panel {
    flex: 1;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-form-container {
    background: white;
    border-radius: 24px;
    padding: 3rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 420px;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 4px;
        background: #14b8a6;
        border-radius: 0 0 4px 4px;
    }
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.login-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
}

.form-input {
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;

    &:focus {
        outline: none;
        border-color: #14b8a6;
        box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.1);
    }

    &::placeholder {
        color: #9ca3af;
    }

    &:disabled {
        background: #f9fafb;
        color: #6b7280;
        cursor: not-allowed;
    }
}

.password-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input {
    padding-right: 3rem;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;

    &:hover {
        color: #14b8a6;
    }

    &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }

    i {
        font-size: 1.125rem;
    }
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: #374151;

    input[type="checkbox"] {
        margin-right: 0.5rem;
        width: 1rem;
        height: 1rem;
        accent-color: #14b8a6;
    }
}

.forgot-password {
    color: #14b8a6;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s ease;

    &:hover {
        color: #0d9488;
        text-decoration: underline;
    }
}

.login-button {
    background: #14b8a6;
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:hover:not(:disabled) {
        background: #0d9488;
        transform: translateY(-1px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
        transform: translateY(0);
    }

    &:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    &.loading {
        background: #6b7280;
    }

    i {
        font-size: 0.875rem;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .login-container {
        flex-direction: column;
    }

    .left-panel {
        min-height: 40vh;
        padding: 2rem;
    }

    .brand-title {
        font-size: 3rem;
    }

    .brand-subtitle {
        font-size: 1.25rem;
    }

    .modules-section {
        justify-content: center;
    }

    .right-panel {
        min-height: 60vh;
        padding: 1rem;
    }

    .login-form-container {
        padding: 2rem;
        max-width: 100%;
    }
}

@media (max-width: 640px) {
    .left-panel {
        padding: 1.5rem;
        min-height: 35vh;
    }

    .brand-title {
        font-size: 2.5rem;
    }

    .brand-subtitle {
        font-size: 1.125rem;
    }

    .brand-description {
        font-size: 0.875rem;
    }

    .modules-section {
        gap: 0.5rem;
    }

    .module-card {
        padding: 0.75rem 1rem;
        min-width: 100px;
    }

    .login-form-container {
        padding: 1.5rem;
        border-radius: 16px;
    }

    .login-title {
        font-size: 1.5rem;
    }
}
