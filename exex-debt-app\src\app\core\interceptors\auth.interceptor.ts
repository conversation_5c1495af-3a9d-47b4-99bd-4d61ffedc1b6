import { AuthService } from '@app/core/services/auth.service';
//ng generate interceptor my-interceptor --skip-tests

import { inject, Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, <PERSON>ttpHandler } from '@angular/common/http';
import { Auth } from '../enums/auth.enum';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    authService = inject(AuthService);
    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {
        // Skip adding Authorization header for auth endpoints
        const isAuthEndpoint = request.url.includes('/api/auth/');
        const token = localStorage.getItem(Auth.ACCESS_TOKEN);

        if (!isAuthEndpoint && token) {
            const authRequest = request.clone({
                headers: request.headers.set('Authorization', `Bearer ${token}`),
            });
            return next.handle(authRequest);
        }

        return next.handle(request);
    }
}
