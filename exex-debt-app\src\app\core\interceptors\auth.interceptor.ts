import { AuthService } from '@app/core/services/auth.service';
//ng generate interceptor my-interceptor --skip-tests

import { inject, Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, <PERSON>ttpHandler } from '@angular/common/http';
import { Auth } from '../enums/auth.enum';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    authService = inject(AuthService);
    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {
        // Skip adding Authorization header for login and refresh endpoints only
        const isLoginEndpoint = request.url.includes('/api/auth/login');
        const isRefreshEndpoint = request.url.includes('/api/auth/refresh');
        const token = localStorage.getItem(Auth.ACCESS_TOKEN);

        if (!isLoginEndpoint && !isRefreshEndpoint && token) {
            const authRequest = request.clone({
                headers: request.headers.set('Authorization', `Bear<PERSON> ${token}`),
            });
            return next.handle(authRequest);
        }

        return next.handle(request);
    }
}
