{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CustomerService {\n  constructor(http) {\n    this.http = http;\n  }\n  getCustomerData() {\n    const mockData = Array.from({\n      length: 11\n    }).map((_, i) => {\n      return {\n        id: i,\n        customerId: 'COD' + i,\n        customerName: 'Customer' + i,\n        phoneNumber: '039876549' + i,\n        email: 'nguyenvan' + i + '@gmail.com',\n        address: 'HCM',\n        status: 'Active',\n        creditLimit: 1000000,\n        currentBalance: 4900 + i,\n        currencyId: 'Cur' + i\n      };\n    });\n    return mockData;\n  }\n  getInvoiceData() {\n    const mockData = Array.from({\n      length: 11\n    }).map((_, i) => {\n      return {\n        id: i,\n        invoiceId: 'Inv' + i,\n        customerId: 'Customer' + i,\n        invoiceDate: '02/02/2025',\n        dueDate: '02/02/2025',\n        totalAmount: 4900 + i,\n        paidAmount: 2900 + i,\n        remainingAmount: 1000000,\n        status: 'Active',\n        currencyId: 'Cur' + i\n      };\n    });\n    return mockData;\n  }\n  getCustomers() {\n    return this.http.get('/customers').pipe(map(response => {\n      if (response.success && response.data && response.data.content) {\n        return response.data.content;\n      } else {\n        throw new Error(response.message || 'Failed to fetch customers');\n      }\n    }));\n  }\n  // Method to get customers with pagination info\n  getCustomersWithPagination() {\n    return this.http.get('/customers').pipe(map(response => {\n      if (response.success && response.data) {\n        return response.data;\n      } else {\n        throw new Error(response.message || 'Failed to fetch customers');\n      }\n    }));\n  }\n  getInvoices() {\n    return Promise.resolve(this.getInvoiceData());\n  }\n  static #_ = this.ɵfac = function CustomerService_Factory(t) {\n    return new (t || CustomerService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CustomerService,\n    factory: CustomerService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["map", "CustomerService", "constructor", "http", "getCustomerData", "mockData", "Array", "from", "length", "_", "i", "id", "customerId", "customerName", "phoneNumber", "email", "address", "status", "creditLimit", "currentBalance", "currencyId", "getInvoiceData", "invoiceId", "invoiceDate", "dueDate", "totalAmount", "paidAmount", "remainingAmount", "getCustomers", "get", "pipe", "response", "success", "data", "content", "Error", "message", "getCustomersWithPagination", "getInvoices", "Promise", "resolve", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\customer.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\n// Interface for individual customer data\r\nexport interface Customer {\r\n    customerId: number;\r\n    customerCode: string;\r\n    customerName: string;\r\n    address: string;\r\n    phone: string;\r\n    email: string;\r\n    saler: string;\r\n    creditLimit: number;\r\n    creditBalance: number;\r\n    creditDate: string;\r\n    createdBy: number;\r\n    createdDate: string;\r\n    updatedBy: number;\r\n    updatedDate: string;\r\n}\r\n\r\n// Interface for pagination data\r\nexport interface Pagination {\r\n    currentPage: number;\r\n    rowPerPage: number;\r\n    totalPage: number;\r\n    totalRow: number;\r\n    sortBy: string;\r\n    sortDirection: string;\r\n}\r\n\r\n// Interface for the data section of API response\r\nexport interface CustomerData {\r\n    content: Customer[];\r\n    pagination: Pagination;\r\n}\r\n\r\n// Interface for the complete API response\r\nexport interface CustomerApiResponse {\r\n    success: boolean;\r\n    message: string;\r\n    data: CustomerData;\r\n}\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class CustomerService {\r\n    constructor(private http: HttpClient) {}\r\n\r\n    getCustomerData() {\r\n        const mockData = Array.from({ length: 11 }).map((_, i) => {\r\n            return {\r\n                id: i,\r\n                customerId: 'COD' + i,\r\n                customerName: 'Customer' + i,\r\n                phoneNumber: '039876549' + i,\r\n                email: 'nguyenvan' + i + '@gmail.com',\r\n                address: 'HCM',\r\n                status: 'Active',\r\n                creditLimit: 1000000,\r\n                currentBalance: 4900 + i,\r\n                currencyId: 'Cur' + i,\r\n            };\r\n        });\r\n\r\n        return mockData;\r\n    }\r\n\r\n    getInvoiceData() {\r\n        const mockData = Array.from({ length: 11 }).map((_, i) => {\r\n            return {\r\n                id: i,\r\n                invoiceId: 'Inv' + i,\r\n                customerId: 'Customer' + i,\r\n                invoiceDate: '02/02/2025',\r\n                dueDate: '02/02/2025',\r\n                totalAmount: 4900 + i,\r\n                paidAmount: 2900 + i,\r\n                remainingAmount: 1000000,\r\n                status: 'Active',\r\n                currencyId: 'Cur' + i,\r\n            };\r\n        });\r\n\r\n        return mockData;\r\n    }\r\n\r\n    getCustomers(): Observable<Customer[]> {\r\n        return this.http.get<CustomerApiResponse>('/customers').pipe(\r\n            map(response => {\r\n                if (response.success && response.data && response.data.content) {\r\n                    return response.data.content;\r\n                } else {\r\n                    throw new Error(response.message || 'Failed to fetch customers');\r\n                }\r\n            })\r\n        );\r\n    }\r\n\r\n    // Method to get customers with pagination info\r\n    getCustomersWithPagination(): Observable<CustomerData> {\r\n        return this.http.get<CustomerApiResponse>('/customers').pipe(\r\n            map(response => {\r\n                if (response.success && response.data) {\r\n                    return response.data;\r\n                } else {\r\n                    throw new Error(response.message || 'Failed to fetch customers');\r\n                }\r\n            })\r\n        );\r\n    }\r\n\r\n    getInvoices() {\r\n        return Promise.resolve(this.getInvoiceData());\r\n    }\r\n}\r\n"], "mappings": "AAGA,SAASA,GAAG,QAAQ,gBAAgB;;;AA8CpC,OAAM,MAAOC,eAAe;EACxBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEvCC,eAAeA,CAAA;IACX,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,CAAC,CAACR,GAAG,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAI;MACrD,OAAO;QACHC,EAAE,EAAED,CAAC;QACLE,UAAU,EAAE,KAAK,GAAGF,CAAC;QACrBG,YAAY,EAAE,UAAU,GAAGH,CAAC;QAC5BI,WAAW,EAAE,WAAW,GAAGJ,CAAC;QAC5BK,KAAK,EAAE,WAAW,GAAGL,CAAC,GAAG,YAAY;QACrCM,OAAO,EAAE,KAAK;QACdC,MAAM,EAAE,QAAQ;QAChBC,WAAW,EAAE,OAAO;QACpBC,cAAc,EAAE,IAAI,GAAGT,CAAC;QACxBU,UAAU,EAAE,KAAK,GAAGV;OACvB;IACL,CAAC,CAAC;IAEF,OAAOL,QAAQ;EACnB;EAEAgB,cAAcA,CAAA;IACV,MAAMhB,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,CAAC,CAACR,GAAG,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAI;MACrD,OAAO;QACHC,EAAE,EAAED,CAAC;QACLY,SAAS,EAAE,KAAK,GAAGZ,CAAC;QACpBE,UAAU,EAAE,UAAU,GAAGF,CAAC;QAC1Ba,WAAW,EAAE,YAAY;QACzBC,OAAO,EAAE,YAAY;QACrBC,WAAW,EAAE,IAAI,GAAGf,CAAC;QACrBgB,UAAU,EAAE,IAAI,GAAGhB,CAAC;QACpBiB,eAAe,EAAE,OAAO;QACxBV,MAAM,EAAE,QAAQ;QAChBG,UAAU,EAAE,KAAK,GAAGV;OACvB;IACL,CAAC,CAAC;IAEF,OAAOL,QAAQ;EACnB;EAEAuB,YAAYA,CAAA;IACR,OAAO,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAsB,YAAY,CAAC,CAACC,IAAI,CACxD9B,GAAG,CAAC+B,QAAQ,IAAG;MACX,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC5D,OAAOH,QAAQ,CAACE,IAAI,CAACC,OAAO;OAC/B,MAAM;QACH,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,2BAA2B,CAAC;;IAExE,CAAC,CAAC,CACL;EACL;EAEA;EACAC,0BAA0BA,CAAA;IACtB,OAAO,IAAI,CAAClC,IAAI,CAAC0B,GAAG,CAAsB,YAAY,CAAC,CAACC,IAAI,CACxD9B,GAAG,CAAC+B,QAAQ,IAAG;MACX,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACnC,OAAOF,QAAQ,CAACE,IAAI;OACvB,MAAM;QACH,MAAM,IAAIE,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,2BAA2B,CAAC;;IAExE,CAAC,CAAC,CACL;EACL;EAEAE,WAAWA,CAAA;IACP,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACnB,cAAc,EAAE,CAAC;EACjD;EAAC,QAAAZ,CAAA,G;qBApEQR,eAAe,EAAAwC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAf5C,eAAe;IAAA6C,OAAA,EAAf7C,eAAe,CAAA8C,IAAA;IAAAC,UAAA,EAFZ;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}