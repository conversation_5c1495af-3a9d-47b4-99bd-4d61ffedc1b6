import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable, tap, catchError, throwError } from 'rxjs';
import { Auth } from '../enums/auth.enum';
import { Path } from '../enums/path.enum';
import { LoginRequest, LoginResponse, LogoutResponse, RefreshTokenResponse } from '../models/auth.model';

@Injectable({
    providedIn: 'root',
})
export class AuthService {
    private jwtToken;
    redirectTo = inject(Router);

    constructor(
        private http: HttpClient,
        private spinner: NgxSpinnerService,
    ) {
        const token: any = localStorage.getItem(Auth.ACCESS_TOKEN);
        if (token) {
            this.jwtToken = token;
        }
    }

    refreshToken(): Observable<RefreshTokenResponse> {
        const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);
        if (!refreshToken) {
            return throwError(() => new Error('No refresh token available'));
        }

        return this.http.post<RefreshTokenResponse>('api/auth/refresh', { refreshToken }).pipe(
            tap((response) => {
                if (response.success && response.data) {
                    localStorage.setItem(Auth.ACCESS_TOKEN, response.data.accessToken);
                    localStorage.setItem(Auth.REFRESH_TOKEN, response.data.refreshToken);
                    this.jwtToken = response.data.accessToken;
                }
            }),
            catchError((error) => {
                this.logoutImmediate();
                return throwError(() => error);
            })
        );
    }

    public isAuthenticated(): boolean {
        return this.jwtToken != null;
    }

    redirectToDashboard() {
        this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);
        this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);
    }

    login(credentials: LoginRequest): Observable<LoginResponse> {
        this.spinner.show();

        return this.http.post<LoginResponse>('api/auth/login', credentials).pipe(
            tap((response) => {
                if (response.success && response.data) {
                    this.jwtToken = response.data.accessToken;
                    localStorage.setItem(Auth.ACCESS_TOKEN, response.data.accessToken);
                    localStorage.setItem(Auth.REFRESH_TOKEN, response.data.refreshToken);
                    this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);
                }
                this.spinner.hide();
            }),
            catchError((error) => {
                this.spinner.hide();
                return throwError(() => error);
            })
        );
    }

    logout(): Observable<LogoutResponse> {
        return this.http.post<LogoutResponse>('api/auth/logout', {}).pipe(
            tap((response) => {
                // Clear tokens regardless of API response
                this.jwtToken = null;
                localStorage.removeItem(Auth.ACCESS_TOKEN);
                localStorage.removeItem(Auth.REFRESH_TOKEN);
                this.redirectTo.navigate([Path.AUTH_LOGIN]);
            }),
            catchError((error) => {
                // Clear tokens even if logout API fails
                this.jwtToken = null;
                localStorage.removeItem(Auth.ACCESS_TOKEN);
                localStorage.removeItem(Auth.REFRESH_TOKEN);
                this.redirectTo.navigate([Path.AUTH_LOGIN]);
                return throwError(() => error);
            })
        );
    }

    // Method for immediate logout without API call (for error scenarios)
    logoutImmediate(): void {
        this.jwtToken = null;
        localStorage.removeItem(Auth.ACCESS_TOKEN);
        localStorage.removeItem(Auth.REFRESH_TOKEN);
        this.redirectTo.navigate([Path.AUTH_LOGIN]);
    }

    mockUser(): Observable<any> {
        return this.http.get('https://jsonplaceholder.typicode.com/users');
    }
}
