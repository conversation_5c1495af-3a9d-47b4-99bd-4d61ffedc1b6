import { Component } from '@angular/core';
import { AuthService } from '@app/core/services/auth.service';
import { LayoutService } from 'src/app/layout/app.layout.service';
import { LoginRequest } from '@app/core/models/auth.model';
import { StorageUtil } from '@app/core/utils/storage.util';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styles: [
        `
            :host ::ng-deep .pi-eye,
            :host ::ng-deep .pi-eye-slash {
                transform: scale(1.6);
                margin-right: 1rem;
                color: var(--primary-color) !important;
            }
        `,
    ],
})
export class LoginComponent {
    valCheck: string[] = ['remember'];
    username: string = '';
    password: string = '';
    errorMessage: string = '';
    isLoading: boolean = false;

    constructor(
        public layoutService: LayoutService,
        private authService: AuthService,
    ) {
        // Only redirect if already authenticated
        if (this.authService.isAuthenticated()) {
            console.log('User already authenticated, redirecting to dashboard');
            this.authService.redirectToDashboard();
        }
    }

    login() {
        if (!this.username || !this.password) {
            this.errorMessage = 'Vui lòng nhập đầy đủ thông tin đăng nhập';
            return;
        }

        this.isLoading = true;
        this.errorMessage = '';

        const credentials: LoginRequest = {
            username: this.username,
            password: this.password
        };

        this.authService.login(credentials).subscribe({
            next: (response) => {
                this.isLoading = false;
                console.log('Login successful:', response);
                console.log('Token stored:', StorageUtil.getAccessToken() ? '✅ Present' : '❌ Missing');
                console.log('Is authenticated:', this.authService.isAuthenticated());
                // Debug storage state
                StorageUtil.debugStorage();
                // Navigation is handled in the service
            },
            error: (error) => {
                this.isLoading = false;
                this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';
                console.error('Login error:', error);
            }
        });
    }
}
