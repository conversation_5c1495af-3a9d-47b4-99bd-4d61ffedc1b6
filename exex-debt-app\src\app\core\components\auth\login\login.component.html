<div class="surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden">
    <div class="flex flex-column align-items-center justify-content-center">
        <div style="border-radius: 56px; padding: 0.3rem">
            <div class="w-full surface-card py-8 px-5 sm:px-8" style="border-radius: 53px">
                <div class="text-center mb-5">
                    <div class="text-900 text-3xl font-medium mb-3">Welcome, exex-debt!</div>
                    <span class="text-600 font-medium">Sign in to continue</span>
                </div>

                <div>
                    <!-- Error message display -->
                    <div *ngIf="errorMessage" class="p-3 mb-3 text-red-500 bg-red-50 border border-red-200 rounded">
                        {{ errorMessage }}
                    </div>

                    <label for="username" class="block text-900 text-xl font-medium mb-2">Tên đăng nhập</label>
                    <input
                        id="username"
                        type="text"
                        placeholder="Nhập tên đăng nhập"
                        [(ngModel)]="username"
                        pInputText
                        class="w-full md:w-30rem mb-5"
                        style="padding: 1rem"
                        [disabled]="isLoading" />

                    <label for="password1" class="block text-900 font-medium text-xl mb-2">Mật khẩu</label>
                    <p-password
                        id="password1"
                        [(ngModel)]="password"
                        placeholder="Nhập mật khẩu"
                        [toggleMask]="true"
                        styleClass="mb-5"
                        inputStyleClass="w-full p-3 md:w-30rem"
                        [disabled]="isLoading"></p-password>

                    <div class="flex align-items-center justify-content-between mb-5 gap-5">
                        <div class="flex align-items-center">
                            <p-checkbox id="rememberme1" [binary]="true" styleClass="mr-2"></p-checkbox>
                            <label for="rememberme1">Remember me</label>
                        </div>
                        <!-- <a
                            class="font-medium no-underline ml-2 text-right cursor-pointer"
                            style="color: var(--primary-color)"
                            >Forgot password?</a
                        > -->
                    </div>
                    <button
                        pButton
                        pRipple
                        [label]="isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'"
                        class="w-full p-3 text-xl"
                        (click)="login()"
                        [disabled]="isLoading"
                        [loading]="isLoading"></button>
                </div>
            </div>
        </div>
    </div>
</div>
